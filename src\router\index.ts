import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      name: 'index',
      redirect: '/gas',
    },
    {
      path: '/gas',
      name: 'gas',
      meta: {
        title: '智慧燃气',
      },
      component: () => import('@/views/dashboard/index.vue'),
    },
    {
      path: '/run-monitor',
      name: 'runMon',
      meta: {
        title: '运行监测',
      },
      component: () => import('@/views/runMonitor/index.vue'),
    },
    {
      path: '/situation-monitor',
      name: 'situation-monitor',
      meta: {
        title: '智慧燃气',
      },
      component: () => import('@/views/situation-monitor/index.vue'),
    },
    {
      path: '/emergency',
      name: 'emergency',
      meta: {
        title: '应急管理',
      },
      component: () => import('@/views/emergency/index.vue'),
    },
    {
      path: '/indoor-check',
      name: 'indoor-check',
      meta: {
        title: '入户安检',
      },
      component: () => import('@/views/indoor-check/index.vue'),
    },
    {
      path: '/battle-gas',
      name: 'battle-gas',
      meta: {
        title: '瓶装石油气',
      },
      component: () => import('@/views/battle-gas/index.vue'),
    },
    {
      path: '/transport',
      name: 'transport',
      meta: { title: '运输车辆' },
      component: () => import('@/views/transportVehicles/index.vue'),
    },
    {
      path: '/evaluate',
      name: 'evaluate',
      meta: {
        title: '安全评价',
      },
      component: () => import('@/views/evaluate/index.vue'),
    },
    {
      path: '/patrol-inspection',
      name: 'patrol-inspection',
      meta: { title: '巡查巡检' },
      component: () => import('@/views/patrol-inspection/index.vue'),
    },
    {
      path: '/double-prevention',
      name: 'double-prevention',
      meta: {
        title: '双重预防',
      },
      component: () => import('@/views/double-prevention/index.vue'),
    },
    {
      path: '/hazard',
      name: 'hazard',
      meta: {
        title: '重点防控',
      },
      component: () => import('@/views/hazard/index.vue'),
    },
    {
      path: '/:pathMatch(.*)*',
      name: '404',
      meta: {
        title: '404 Not Found',
      },
      component: () => import('@/views/404/index.vue'),
    },
  ],
})

export default router
