<template>
  <div class="panel-container">
    <div class="flex items-center justify-between panel-header">
      <div class="header-title">到访不遇用户</div>
      <div class="header-dropdown">
        <div class="btn-group">
          <button
            class="period-btn"
            :class="{ active: selectedOpt === 'administrative' }"
            @click="selectedOpt = 'administrative'"
          >
            行政区划
          </button>
          <button
            class="period-btn"
            :class="{ active: selectedOpt === 'enterprise' }"
            @click="selectedOpt = 'enterprise'"
          >
            燃气企业
          </button>
        </div>
      </div>
    </div>
    <div class="p-4 panel-content">
      <div class="equipment-alarm-container">
        <!-- 左侧统计卡片区域 -->
        <div class="user-overview-container">
          <div v-for="(item, index) in userStats" :key="index" class="user-card">
            <div class="card-content">
              <div class="card-icon">
                <img :src="item.icon" alt="" />
                <div v-if="item.secondIcon" class="second-icon">
                  <img :src="item.secondIcon" alt="" />
                </div>
              </div>
              <div class="card-info">
                <div class="card-title">{{ item.title }}</div>
                <div class="card-value-container">
                  <div class="card-value">{{ item.value }}</div>
                  <div class="card-unit">{{ item.unit }}</div>
                </div>
              </div>
            </div>
            <img :src="iconData.union" alt="" class="card-border-bottom" />
            <div class="card-side-lines">
              <img :src="iconData.vectorLeft" alt="" class="side-line left" />
              <img :src="iconData.vectorRight" alt="" class="side-line right" />
            </div>
          </div>
        </div>
        <!-- 右侧图表区域 -->
        <div class="chart-panel">
          <div class="chart-content">
            <div ref="chartRef" class="chart-container"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'

interface UserStat {
  type: string
  title: string
  value: string
  icon: string
  secondIcon: string
  unit?: string // 添加可选的 unit 属性
}

const chartRef = ref<HTMLElement>()
const selectedOpt = ref<string>('administrative')
let chart: echarts.ECharts | null = null
// tooltip 轮播定时器
let tooltipTimer: ReturnType<typeof setInterval> | null = null
// 当前展示的 tooltip 索引
let currentIndex = -1

// Mock数据
let xData = ['2019', '2020', '2021', '2022', '2023', '2024']
let data = [120, 200, 150, 280, 350, 420]

const option = {
  title: {
    text: '单位：个',
    textStyle: {
      color: '#FFFFFF',
      fontSize: 12,
      fontWeight: 'normal',
    },
  },
  tooltip: {
    trigger: 'axis',
    backgroundColor: 'rgba(13, 81, 115, 0.6)',
    color: '#FFFFFF',
    borderWidth: 2,
    borderColor: '#1AB3FF',
    textStyle: {
      color: '#FFFFFF',
      fontSize: 12,
    },
    confine: true,
    formatter: function (val: any) {
      let backString = ''
      val.forEach((item: any) => {
        if (item.seriesName === 'wuzi') {
          backString += item.marker + ` ${item.data}`
        }
      })
      return `${val[0]?.name}</br>` + backString
    },
  },
  grid: {
    left: '8%',
    right: '5%',
    bottom: '8%',
    top: '18%',
    containLabel: true,
  },
  xAxis: {
    type: 'category',
    data: xData,
    axisLabel: {
      interval: 0,
      color: '#FFFFFF',
      fontSize: 12,
    },
    axisTick: {
      show: false,
    },
    axisLine: {
      lineStyle: {
        color: '#2580E8',
      },
    },
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      fontSize: 12,
      color: '#FFFFFF',
    },
    splitLine: {
      lineStyle: {
        type: 'dashed',
        color: 'rgba(166, 207, 255, 0.4)',
      },
    },
  },
  series: [
    {
      type: 'bar',
      z: 1,
      itemStyle: {
        color: 'rgba(26, 178, 255, 0.15)',
      },

      barWidth: 22,
      data: new Array(6).fill(Math.max(...data) * 1.2),
      markPoint: {
        symbol: 'rect',
        symbolSize: [24, 2],
        label: {
          show: false,
        },
        data: new Array(6).fill(Math.max(...data) * 1.2).map((v, i) => {
          return {
            value: v,
            xAxis: v,
            yAxis: i,
            itemStyle: {
              color: '#02A7F0',
            },
          }
        }),
      },
    },
    {
      type: 'pictorialBar',
      symbol: 'triangle',
      symbolSize: [16, 6],
      symbolOffset: [1.96, -5.4],
      symbolPosition: 'end',
      z: 3,
      color: '#1AB2FF',
      data: data,
    },
    {
      name: 'wuzi',
      type: 'bar',
      z: 2,
      barGap: '-85%',
      barWidth: 16,
      itemStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 0,
              color: '#1AB2FF',
            },
            {
              offset: 1,
              color: 'rgba(26, 178, 255, 0)',
            },
          ],
          global: false,
        },
      },
      data: data,
    },
  ],
}

// 图标数据配置 - 使用图片路径
const iconData = {
  unUser: '/src/assets/indoor-icon/un-user.svg',
  backUser: '/src/assets/indoor-icon/back-user.svg',
  iconDown: '/src/assets/indoor-icon/user-bg.svg',
  vectorLeft: '/src/assets/industry-icons/Vector-Left.svg',
  vectorRight: '/src/assets/industry-icons/Vector-right.svg',
  union: '/src/assets/industry-icons/Union.svg',
}

const userStats = ref<UserStat[]>([
  {
    type: 'total',
    title: '到访不遇用户（个）',
    value: '1,245,678',
    icon: iconData.iconDown,
    secondIcon: iconData.unUser,
    unit: '个',
  },
  {
    type: 'pipeline',
    title: '已回访（户）',
    value: '1,245,678',
    icon: iconData.iconDown,
    secondIcon: iconData.backUser,
    unit: '户',
  },
])

const initChart = () => {
  if (!chartRef.value) return

  chart = echarts.init(chartRef.value)

  chart.setOption(option)
  startTooltipAnimation()
}

// 开始 tooltip 轮播
const startTooltipAnimation = () => {
  const dataCount = xData.length
  if (!chart || dataCount === 0) return

  // 清理之前的定时器
  if (tooltipTimer) {
    clearInterval(tooltipTimer)
  }

  tooltipTimer = setInterval(() => {
    // 取消之前的高亮
    chart?.dispatchAction({
      type: 'downplay',
      seriesIndex: 2, // 使用主要的bar系列
      dataIndex: currentIndex,
    })

    // 循环移动到下一个点
    currentIndex = (currentIndex + 1) % dataCount

    // 显示新的 tooltip
    chart?.dispatchAction({
      type: 'showTip',
      seriesIndex: 2, // 在主要的bar系列上显示 tooltip
      dataIndex: currentIndex,
    })

    // 高亮当前数据项
    chart?.dispatchAction({
      type: 'highlight',
      seriesIndex: 2, // 使用主要的bar系列
      dataIndex: currentIndex,
    })
  }, 3000) // 每隔3秒执行
}

const handleResize = () => {
  chart?.resize()
}

onMounted(() => {
  initChart()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  chart?.dispose()
  window.removeEventListener('resize', handleResize)
  if (tooltipTimer) {
    clearInterval(tooltipTimer)
  }
})
</script>

<style lang="scss" scoped>
@import '@/styles/index.css';

.equipment-alarm-container {
  display: flex;
  width: 100%;
  height: 100%;
  gap: 20px;
}

.chart-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .chart-content {
    flex: 1;
    padding: 4px;

    .chart-container {
      width: 100%;
      height: 100%;
    }
  }
}

.user-overview-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 33.33%;
  flex-shrink: 0;
}

.user-card {
  position: relative;
  width: 100%;
  height: 104px;
  overflow: hidden;

  .card-content {
    position: absolute;
    top: 20px;
    left: 20px;
    right: 20px;
    height: 64px;
    display: flex;
    flex-direction: row;
    justify-content: start;
    align-items: center;
    gap: 16px;

    .card-icon {
      flex-shrink: 0;
      width: 48px;
      height: 48px;
      display: flex;
      position: relative;
      align-items: center;
      justify-content: center;

      > img {
        position: absolute;
        top: 0;
        left: 0;
        width: 48px;
        height: 48px;
        z-index: 1;
      }

      .second-icon {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 24px;
        height: 24px;
        z-index: 2;

        img {
          width: 100%;
          height: 100%;
        }
      }
    }

    .card-info {
      flex: 1;
      height: 48px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: flex-start;

      .card-title {
        width: 100%;
        height: 20px;
        white-space: nowrap;
        color: #66ffff;
        font-family: 'Noto Sans SC';
        font-size: 14px;
        line-height: 20px;
        text-align: left;
        margin-bottom: 4px;
      }

      .card-value-container {
        width: 100%;
        height: 24px;
        display: flex;
        flex-direction: row;
        justify-content: start;
        align-items: baseline;

        .card-value {
          color: #ffffff;
          font-family: 'DINPro';
          font-size: 18px;
          line-height: 24px;
          font-weight: bold;
          margin-right: 4px;
        }

        .card-unit {
          color: #ffffff;
          font-family: 'Noto Sans SC';
          font-size: 14px;
          line-height: 20px;
        }
      }
    }
  }

  .card-border-bottom {
    position: absolute;
    left: 30px;
    bottom: 0px;
    width: calc(100% - 60px);
    height: 12px;
  }

  .card-side-lines {
    .side-line {
      position: absolute;
      top: 0px;
      width: 5.5px;
      height: 115px;

      &.left {
        left: 0px;
      }

      &.right {
        right: 0px;
      }
    }
  }
}
.btn-group {
  display: flex;
  gap: 8px;
}

.period-btn {
  border: 1px solid #409fff;
  background: rgba(64, 159, 255, 0.15);
  color: #fff;
  font-size: 12px;
  font-weight: 300;
  padding: 4px 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  outline: none;
  border-radius: 4px;
  text-align: center;
  height: 28px;
  line-height: 0px;
}

.period-btn:hover {
  background: rgba(74, 158, 255, 0.1);
}
.period-btn.active {
  background: rgba(255, 198, 26, 0.15);
  color: #fff;
  border-color: #ffd700;
}
</style>
