// 定义点位数据接口
export interface PointData {
  id: string
  name: string
  location: [number, number] // [经度, 纬度]
  type: string
  status?: string
}

// 定义管线数据接口
export interface PipelineData {
  id: string
  name: string
  type: string
  path: [number, number][] // 路径点的数组，每个点是 [经度, 纬度]
  pressure: 'high' | 'medium' // 管网压力类型
}

// 加载供气企业数据
export async function loadGasCompanies(): Promise<PointData[]> {
  try {
    const response = await fetch('/mock/gasCompany.json')
    const data = await response.json()
    return data.data.map((item: any) => ({
      id: item.properties.id,
      name: item.properties.qymc,
      type: 'gasCompany',
      location: item.geometry.coordinates.map(Number)
    }))
  } catch (error) {
    console.error('加载供气企业数据失败:', error)
    return []
  }
}

// 加载场站数据
export async function loadStations(): Promise<PointData[]> {
  try {
    const response = await fetch('/mock/station.json')
    const data = await response.json()
    return data.data.map((item: any) => ({
      id: item.properties.id,
      name: item.properties.czmc,
      type: 'station',
      location: item.geometry.coordinates.map(Number)
    }))
  } catch (error) {
    console.error('加载场站数据失败:', error)
    return []
  }
}

// 加载管线数据
export async function loadPipelines(): Promise<PipelineData[]> {
  try {
    const response = await fetch('/mock/pipeline.json')
    const data = await response.json()
    return [...data['high-pressure'], ...data['medium-pressure'] || []].map((item: any) => {
      const geometry = JSON.parse(item.geometry)
      return {
        id: item.properties.gdbm,
        name: item.properties.gdmc,
        type: 'pipeline',
        path: geometry.coordinates,
        pressure: item.properties.yldj === '140300' ? 'high' : 'medium'
      }
    })
  } catch (error) {
    console.error('加载管线数据失败:', error)
    return []
  }
}