<template>
  <div class="panel-container-half-lr">
    <div class="panel-header">企业服务车辆概况</div>
    <div class="p-4 panel-content list">
      <div class="item" v-for="(item, idx) in list" :key="idx">
        <div class="item-head">
          <div class="label">{{ item.label }}</div>
          <div class="val">{{ item.value }}</div>
        </div>
        <div class="bar">
          <div class="fill" :style="{ width: `${item.percent}%` }">
            <span class="split"></span>
          </div>
        </div>
      </div>
    </div>
  </div>
 </template>

<script setup lang="ts">
const list = Array.from({ length: 6 }).map(() => ({ label: '这是对应内容', value: 33340, percent: 65 }))
</script>

<style scoped>
@import '@/styles/index.css';
.list { display: flex; flex-direction: column; gap: 15px; }
.item-head { display: flex; align-items: center; justify-content: space-between; margin-bottom: 4px; }
.label { color: #e7f2ff; font-size: 14px; }
.val { color: #D4FF8E; font-weight: 700; font-size: 14px; }
.bar { width: 100%; height: 25px; background: rgba(64,159,255,.18); border: 1px solid rgba(64,159,255,.35); border-radius: 4px; overflow: hidden; position: relative; }
/* 让进度条略细于外部容器 */
.fill { display: block; height: calc(100% - 6px); margin: 3px 0; background: linear-gradient(90deg, #D9FFB8 0%, #BFF09F 100%); border-radius: 2px; position: relative; transition: width .4s ease; }
/* 竖向分隔条高度与外部容器一致（覆盖内条上下留白） */
.split { position: absolute; right: 0; top: -3px; width: 4px; height: calc(100% + 6px); background: #D9FFB8; }
</style>


