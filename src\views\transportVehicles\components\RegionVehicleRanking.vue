<template>
  <div class="panel-container-half-lr">
    <div class="panel-header">区域车辆数排行</div>
    <div class="p-4 panel-content list">
      <div v-for="(item, i) in items" :key="i" class="item">
        <div class="item-head">
          <div class="label">{{ item.label }}</div>
          <div class="val">{{ item.value }}</div>
        </div>
        <div class="bar">
          <div class="fill" :style="{ width: `${item.percent}%` }">
            <span class="split"></span>
          </div>
        </div>
      </div>
    </div>
  </div>
 </template>

<script setup lang="ts">
const items = Array.from({ length: 8 }).map((_, i) => ({
  label: '这是对应内容',
  value: 33340,
  percent: 40 + i * 8,
}))
</script>

<style scoped>
@import '@/styles/index.css';
.list { display: flex; flex-direction: column; gap: 10px; }
.item-head { display: flex; align-items: center; justify-content: space-between; margin-bottom: 2px; }
.label { color: #e7f2ff; font-size: 14px; }
.val { color: #FFC61A; font-weight: 700; font-size: 14px; }
.bar { width: 100%; height: 18px; background: rgba(64,159,255,.18); border: 1px solid rgba(64,159,255,.35); border-radius: 4px; overflow: hidden; position: relative; }
.fill { display: block; height: calc(100% - 4px); margin: 2px 0; background: linear-gradient(90deg, #FFE27A 0%, #FFC61A 100%); border-radius: 2px; position: relative; transition: width .4s ease; }
.split { position: absolute; right: 0; top: -3px; width: 3px; height: calc(100% + 6px); background: #FFC61A; }
</style>


