<template>
  <div class="map-container">
    <div id="contain"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'
import AMapLoader from '@amap/amap-jsapi-loader'

const props = defineProps({
  center: {
    type: Array,
    default: () => [114.8, 36.55],
  },
  zoom: {
    type: Number,
    default: 12,
  },
  viewMode: {
    type: String,
    default: '3D',
  },
  pitch: {
    type: Number,
    default: 0,
  },
  clickable: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['map-click', 'map-ready'])

// 常量定义
const defaultView = {
  pos: props.center,
  pitch: props.pitch,
  rotation: 0,
  zoom: props.zoom,
}

// 响应式状态
const map = ref(null)
const loca = ref(null)
const AMap = ref(null)
const autoTourTimer = ref(null)
const markerGeo = ref({})
const marker = ref(null)
const heatmapLayer = ref(null)
const outLayer = ref(null)
const gasCompanyMarkers = ref([])
const stationMarkers = ref([])
const pipelinePolylines = ref([])

// 工具函数
const getRandom = (min, max) => {
  return Math.random() * (max - min) + min
}

// 初始化地图
const initMap = async () => {
  try {
    const AMapInstance = await AMapLoader.load({
      key: '8a6865ecdde60542806eb4dd4da77aed',
      version: '2.0',
      plugins: ['AMap.GeoJSON'],
      Loca: { version: '2.0.0' },
    })

    AMap.value = AMapInstance
    map.value = new AMapInstance.Map('contain', {
      zoom: props.zoom,
      viewMode: props.viewMode,
      pitch: props.pitch,
      rotation: 0,
      center: props.center,
      mapStyle: 'amap://styles/darkblue',
      skyColor: '#081245',
      willReadFrequently: true,
      dragEnable: true,
      zoomEnable: true,
      doubleClickZoom: true,
      keyboardEnable: true,
    })

    // 创建边界线图层
    const geoJSON = await fetch('/geo/feixiang-geometry.json').then(res => res.json())
    outLayer.value = new AMapInstance.Polygon({
      path: geoJSON.geometry.coordinates[0],
      strokeColor: '#5EECFF',
      strokeWeight: 2,
      strokeOpacity: 1,
      fillColor: '#0D3B6A',
      fillOpacity: 0.3,
      zIndex: 1,
    })
    map.value.add(outLayer.value)

    // 在地图加载完成后再创建 Loca 容器
    map.value.on('complete', () => {
      loca.value = new Loca.Container({ map: map.value })
      loca.value.pointLight.intensity = 0
      loca.value.ambLight.intensity = 1
      loca.value.animate.start()

      // 适应边界范围
      map.value.setFitView([outLayer.value], false, [20, 20, 20, 20])

      emit('map-ready')
    })

    map.value.on('complete', () => {
      loca.value.animate.start()
      emit('map-ready')
    })

    if (props.clickable) {
      map.value.on('click', e => {
        // console.log(map.value.getCenter(), map.value.getZoom(),
        //            map.value.getRotation(), map.value.getPitch())
        // 清除上一个标记
        if (marker.value) {
          map.value.remove(marker.value)
        }
        // 创建新的标记
        marker.value = new AMap.value.Marker({
          position: e.lnglat,
        })
        map.value.add(marker.value)
        // 发送事件
        emit('map-click', e.lnglat)
      })
    }
  } catch (error) {
    console.error('地图加载失败:', error)
  }
}

// 重置视图
const resetView = () => {
  goPositionAni(defaultView.pos, defaultView.pitch, defaultView.rotation, defaultView.zoom)
}

// 显示热力图
const showHeatMap = heatData => {
  destroyHeatMap() // 先移除已有的

  if (!heatData || heatData.length === 0) {
    return
  }

  const heatGeo = new Loca.GeoJSONSource({
    data: {
      type: 'FeatureCollection',
      features: heatData.map(item => ({
        type: 'Feature',
        properties: { num: item.num },
        geometry: {
          type: 'Point',
          coordinates: item.lnglat,
        },
      })),
    },
  })

  heatmapLayer.value = new Loca.HeatMapLayer({
    zIndex: 10,
    opacity: 1,
    visible: true,
    zooms: [2, 22],
  })

  heatmapLayer.value.setSource(heatGeo, {
    radius: 30, // 半径
    unit: 'meter',
    height: 100, // 高度
    gradient: {
      0.1: '#2A85B8',
      0.2: '#16B0A9',
      0.3: '#29CF6F',
      0.4: '#5CE182',
      0.5: '#7DF675',
      0.6: '#FFF100',
      0.7: '#FAA53F',
      1: '#D04343',
    },
    value: (index, feature) => feature.properties.num,
    heightBezier: [0, 0.53, 0.37, 0.98],
  })

  loca.value.add(heatmapLayer.value)
}

// 销毁热力图
const destroyHeatMap = () => {
  if (heatmapLayer.value) {
    loca.value.remove(heatmapLayer.value)
    heatmapLayer.value = null
  }
}

// 清理地图数据
const clearLoca = () => {
  loca.value.clear()
}

const addMarker = position => {
  if (marker.value) {
    map.value.remove(marker.value)
  }
  marker.value = new AMap.value.Marker({
    position: [position.lng, position.lat],
  })
  map.value.add(marker.value)
}

const destroyMarker = () => {
  if (marker.value) {
    map.value.remove(marker.value)
    marker.value = null
  }
}

const polygon = ref(null)

// 绘制多边形
const drawPolygon = path => {
  destroyPolygon()
  polygon.value = new AMap.value.Polygon({
    path: path,
    strokeColor: '#00FF00',
    strokeWeight: 2,
    strokeOpacity: 0.8,
    fillColor: '#00FF00',
    fillOpacity: 0.3,
    zIndex: 50,
  })
  map.value.add(polygon.value)
  map.value.setFitView([polygon.value])
}

// 销毁多边形
const destroyPolygon = () => {
  if (polygon.value) {
    map.value.remove(polygon.value)
    polygon.value = null
  }
}

// 添加燃气企业标记
const addGasCompanyMarkers = async () => {
  try {
    const response = await fetch('/mock/gasCompany.json')
    const data = await response.json()

    // 清除现有标记
    clearGasCompanyMarkers()

    data.data.forEach(item => {
      const marker = new AMap.value.Marker({
        position: [parseFloat(item.geometry.coordinates[0]), parseFloat(item.geometry.coordinates[1])],
        icon: new AMap.value.Icon({
          image: '/src/assets/map/gas-icon.png',
          size: new AMap.value.Size(24, 24),
          imageSize: new AMap.value.Size(24, 24),
        }),
        title: item.properties.qymc,
      })

      map.value.add(marker)
      gasCompanyMarkers.value.push(marker)
    })
  } catch (error) {
    console.error('加载燃气企业数据失败:', error)
  }
}

// 添加场站标记
const addStationMarkers = async () => {
  try {
    const response = await fetch('/mock/station.json')
    const data = await response.json()

    // 清除现有标记
    clearStationMarkers()

    data.data.forEach(item => {
      const marker = new AMap.value.Marker({
        position: [parseFloat(item.geometry.coordinates[0]), parseFloat(item.geometry.coordinates[1])],
        icon: new AMap.value.Icon({
          image: '/src/assets/map/station-icon.png',
          size: new AMap.value.Size(24, 24),
          imageSize: new AMap.value.Size(24, 24),
        }),
        title: item.properties.name || '场站',
      })

      map.value.add(marker)
      stationMarkers.value.push(marker)
    })
  } catch (error) {
    console.error('加载场站数据失败:', error)
  }
}

// 添加管线
const addPipelinePolylines = async pressureType => {
  try {
    const response = await fetch('/mock/pipeline.json')
    const data = await response.json()

    const pipelineData = pressureType === 'high-pressure' ? data['high-pressure'] : data['medium-pressure']
    const strokeColor = pressureType === 'high-pressure' ? '#FF0000' : '#00FF00' // 高压红色，中压绿色

    pipelineData?.forEach(item => {
      const geometry = JSON.parse(item.geometry)
      const polyline = new AMap.value.Polyline({
        path: geometry.coordinates,
        strokeColor: strokeColor,
        strokeWeight: 3,
        strokeOpacity: 0.8,
      })

      map.value.add(polyline)
      pipelinePolylines.value.push(polyline)
    })
  } catch (error) {
    console.error('加载管线数据失败:', error)
  }
}

// 清除标记和管线的方法
const clearGasCompanyMarkers = () => {
  gasCompanyMarkers.value.forEach(marker => map.value.remove(marker))
  gasCompanyMarkers.value = []
}

const clearStationMarkers = () => {
  stationMarkers.value.forEach(marker => map.value.remove(marker))
  stationMarkers.value = []
}

const clearPipelinePolylines = () => {
  pipelinePolylines.value.forEach(polyline => map.value.remove(polyline))
  pipelinePolylines.value = []
}

// 生命周期钩子
onMounted(initMap)

defineExpose({
  showHeatMap,
  destroyHeatMap,
  addMarker,
  destroyMarker,
  drawPolygon,
  destroyPolygon,
  addGasCompanyMarkers,
  addStationMarkers,
  addPipelinePolylines,
  clearGasCompanyMarkers,
  clearStationMarkers,
  clearPipelinePolylines,
})

onBeforeUnmount(() => {
  if (loca.value) loca.value.destroy()
  if (map.value) map.value.destroy()
  if (autoTourTimer.value) clearTimeout(autoTourTimer.value)
})
</script>

<style scoped>
.map-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

#contain {
  width: 100%;
  height: 100%;
}
</style>
